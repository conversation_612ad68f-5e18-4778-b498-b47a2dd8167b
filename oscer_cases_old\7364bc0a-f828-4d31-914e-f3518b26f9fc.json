{"metadata": {"name": "Maximus, 21M", "scraped_at": "2025-09-03T00:44:35.679443", "script_url": "https://www.oscer.ai/pwf/script/lgPIF8JfhEZy"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. <PERSON> is a 21 year old male who has presented with a fever. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I am a 21 year old man.", "My pronouns are he/him.", "I study computing at university.", "I have come to the GP today because I have a fever."], "Persona": ["I am a young and fit male.", "I am not anxious.", "I get the occasional cold but this is worse than usual so I am a little concerned.", "I decided to check my temperature today and got quite worried that it was 38.1 degrees Celsius."], "HOPC": ["I began to feel unwell three days ago.", "I have a runny nose and a hacking, dry cough.", "I have not brought up any blood with my cough.", "I just feel generally unwell and all my muscles ache. I don't have muscle weakness, it's more of an ache.", "I'm pretty sure I have a fever. Last time I checked it was 38.1 degrees Celsius.", "I don’t have much energy and I’m unable to really study for university. I'm pretty tired.", "I can’t remember coming into contact with any sick people.", "Nothing seems to make this illness better or worse, panadol helps a bit with the muscle aches. It's pretty much the same since it started.", "I have not travelled recently.", "I do not have any abdominal pain.", "I have not lost my sense of taste or smell.", "I have not been in contact with any covid positive cases. I always wear a mask and socially distance when necessary.", "I haven't noticed any changes in my bowel habits or urination. I don't have any back pain.", "I don't have a headache, any neck stiffness or photophobia."], "PMHx": ["I have no other medical conditions. I've never been to hospital.", "I haven't been sick recently.", "I don't take any medications.", "I get seasonal hayfever but I do not have asthma or eczema.", "I'm not allergic to anything.", "I am up to date with my childhood vaccinations but never get the flu vaccine.", "I am fully vaccinated against <PERSON><PERSON>."], "FMHx": ["My mother is 46 and was diagnosed with high blood pressure when she was 43, she manages it well with lifestyle modifications. My father is 50 and only recently got told he has type 2 diabetes. Now he takes metformin everyday and joins mum on walks.", "I don't have any siblings.", "I don't know of any other medical conditions that run in the family."], "SHx": ["I live at home with my parents.", "I study computing at university.", "I have never smoked.", "I drink 4 to 5 beers on the weekends approximately every fortnight with friends.", "I have never taken any recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Fever", "score": 8, "subcategories": [{"title": "Severity", "score": 1, "subcategories": [{"title": "Quantitative", "score": 1, "items": ["Temperature"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Epsiodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Travel", "Medications", "Recent Illness", "Lifestyle Changes", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Coryzal Symptoms", "score": 2, "items": ["Sneezing", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Chest Pain", "score": 1, "items": ["Tightness", "Chest Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Pneumonia", "Acute Bronchitis", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Antibiotics", "Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Ability to Work", "Duration At Job", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}