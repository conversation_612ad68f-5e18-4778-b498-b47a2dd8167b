{"metadata": {"name": "<PERSON>, 54F", "scraped_at": "2025-09-03T11:58:23.303310", "script_url": "https://www.oscer.ai/pwf/script/b7dd1Aivp7kb"}, "tabs": {"doctor_information": {"Intro": "You are an intern working in the Emergency Department of a regional hospital. <PERSON> is a 54 year old lady (her/she) from home presenting with blood in her vomit. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I'm 54 years old and I'm unemployed.", "I've come in to the doctor's because I've been vomiting with blood in it."], "Persona": ["I hate hospitals but I'm scared by how much blood was in my vomit.", "I’m worried, I've heard that vomiting like this means my liver isn't good at all."], "HOPC": ["The vomiting happened around lunchtime.", "I was feeling off in the morning after I woke up.", "The vomit came out of nowhere and it was enormous.", "I had 3 episodes of massive vomiting.", "There was a large amount of blood in the toilet bowl.", "The vomit was a dark red colour, it definitely wasn’t normal.", "I’m not sure exactly how much blood I vomited up but it was more than a few cups.", "I was totally light headed and dizzy afterwards.", "I called a friend to come take me to hospital straight away, I couldn’t get off the floor because I was so dizzy.", "I have vomited up blood before, but nowhere near this much.", "I first vomited blood 3 weeks ago after a few drinks.", "I didn’t feel dizzy afterwards and I wasn’t concerned by it.", "Have a look, I do feel like I’m more yellow than usual.", "I am really bloated in my tummy, my belly button never pops out as much as it is now.", "I do have these strange veins around my belly button too.", "The bloating is causing me some pain.", "I am feeling like I can’t eat as much with how bloated my tummy is.", "Now you mention it I think my stools have been a little darker than usual.", "I don’t feel confused.", "I don’t have any urinary symptoms.", "I haven’t had any fevers or night sweats."], "PMHx": ["I was diagnosed with hepatitis B and C about 5-10 years ago. The doctors think it had been going on for a while and my liver was cirrhosed on imaging.", "Apart from the hepatitis I don’t have any other conditions.", "They've got me on spironolactone and frusamide tablets I think it's called to deal with the fluid in my belly. I take 200mg of spironolactone and 80mg of furosemide in the morning.", "I don’t have any allergies.", "I haven't been to the hospital before for liver disease.", "I haven't had any surgeries before."], "FMHx": ["I’m not sure about the medical conditions in my family, I’m not in touch with anybody."], "SHx": ["I don’t eat very healthily, I am supposed to cut salt out.", "I live by myself in social housing.", "I divorced from my husband about 15 years ago.", "I have one child.", "I don’t see my husband or child very much.", "I smoke 20 cigarettes a day and I have for the past 40 years.", "I used to drink more, but ever since they found out I have liver problems I’ve cut down.", "After I separated from my husband I used to drink 6 standards a day.", "Since 5 years ago I only drink very occasionally.", "I don’t use drugs anymore.", "I used to inject heroin until 5 years ago."]}, "marking_rubric": {"HOPC": [{"title": "Haematemesis", "score": 11, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Character", "score": 2, "items": ["Consistency", "Fresh Blood", "Coffee Grounds", "Asking Generally", "Presence of Clots"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Blood", "Amount of Vomit", "Scale out of 10", "Cups or Teaspoons"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Change Over Time", "Frequency of Blood", "Duration of Episodes", "Frequency of Vomiting", "Last Episode of Blood"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Painful Swallowing", "score": 1, "items": ["Painful Swallowing"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Malodorous Stools", "Black and Tarry Stools"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["St<PERSON>ch Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Gastritis", "Asking Generally", "Oesophageal Disease", "Peptic Ulcer Disease", "Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["NSAID", "<PERSON><PERSON><PERSON>", "Ibuprofen", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["Endoscopy", "Cancer Screening"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["St<PERSON>ch Cancer", "Asking Generally", "Oesophageal Cancer", "Peptic Ulcer Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Number of Sexual Partners"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}