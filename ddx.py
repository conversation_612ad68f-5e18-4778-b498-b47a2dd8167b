import json
import uuid
import os
import time
import random
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager


def extract_study_gap_patients():
    """Extract patient names and bottom strip colors from Study Gap section"""

    # Setup Chrome options with anti-detection measures
    options = Options()
    options.add_argument("--start-minimized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(
        ChromeDriverManager().install()), options=options)

    # Execute script to remove webdriver property
    driver.execute_script(
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        # 1. Open the sign-in page directly
        driver.get("https://www.oscer.ai/signin")

        # 2. Fill in credentials
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "email"))
        )
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "password"))
        )

        email_input.clear()
        email_input.send_keys("<EMAIL>")
        password_input.clear()
        password_input.send_keys("techrise01badoscer")

        # 3. Click the "Sign in" button
        login_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//button[.//span[text()='Sign in']]"))
        )
        login_button.click()

        # 4. After login, wait for dashboard to load
        WebDriverWait(driver, 20).until(
            EC.url_contains("/dashboard")
        )

        time.sleep(5)  # Allow page to fully load

        # 5. Find the Study Gap section
        study_gap_section = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//div[contains(text(), 'Study Gap')]//ancestor::div[@class='jss671']"))
        )

        # 6. Find all patient cards in the Study Gap section
        patient_cards = study_gap_section.find_elements(
            By.CSS_SELECTOR, ".jss722.jss723")

        patients_data = []

        for card in patient_cards:
            try:
                # Extract patient name
                name_element = card.find_element(By.CSS_SELECTOR, ".jss736")
                patient_name = name_element.text.strip()

                # Skip if this is the "Random Case" card
                if patient_name == "Random Case":
                    continue

                # Extract bottom strip color
                strip_element = card.find_element(By.CSS_SELECTOR, ".jss731")
                style_attr = strip_element.get_attribute("style")

                # Extract RGB color from style attribute
                color_match = re.search(
                    r'border-bottom-color:\s*rgb\(([^)]+)\)', style_attr)
                if color_match:
                    rgb_values = color_match.group(1)
                    color_description = get_color_description(rgb_values)

                    patients_data.append({
                        'name': patient_name,
                        'color_rgb': f"rgb({rgb_values})",
                        'color_description': color_description
                    })

            except Exception as e:
                print(f"Error processing card: {e}")
                continue

        return patients_data

    finally:
        driver.quit()


def get_color_description(rgb_values):
    """Convert RGB values to color description"""
    # Parse RGB values
    r, g, b = map(int, rgb_values.split(', '))

    # Define color mappings based on the observed colors
    if r == 187 and g == 217 and b == 249:
        return "Light Blue"
    elif r == 251 and g == 167 and b == 153:
        return "Light Red/Orange"
    else:
        return f"Custom Color (R:{r}, G:{g}, B:{b})"


def main():
    """Main function to extract and display Study Gap patient data"""
    print("Extracting Study Gap patient data...")
    print("=" * 50)

    try:
        patients = extract_study_gap_patients()

        if patients:
            print(f"Found {len(patients)} patients in Study Gap:")
            print()

            for i, patient in enumerate(patients, 1):
                print(
                    f"{i:2d}. {patient['name']:<20} | {patient['color_description']:<15} | {patient['color_rgb']}")
        else:
            print("No patients found in Study Gap section.")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
