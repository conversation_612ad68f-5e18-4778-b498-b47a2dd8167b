import json
import uuid
import os
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager

# Setup Chrome options with anti-detection measures
options = Options()
options.add_argument("--start-minimized")
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)
options.add_argument(
    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

driver = webdriver.Chrome(service=Service(
    ChromeDriverManager().install()), options=options)

# Execute script to remove webdriver property
driver.execute_script(
    "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

try:
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. After login, go directly to Home page
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )
    # driver.get("https://www.oscer.ai/dashboard/home")

    time.sleep(5)
finally:
    pass  # keep browser open for debugging
