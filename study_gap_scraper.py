import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Setup Chrome driver with anti-detection measures"""
    options = Options()
    options.add_argument("--start-minimized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(
        ChromeDriverManager().install()), options=options)

    # Execute script to remove webdriver property
    driver.execute_script(
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def login_to_oscer(driver):
    """Login to the Oscer platform"""
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. Wait for login to complete
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )

def get_study_gap_patients(driver):
    """Extract patient names from Study Gap section"""
    study_gap_patients = []
    
    try:
        # Wait for Study Gap section to be present
        study_gap_section = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located(
                (By.XPATH, "//div[contains(text(), 'Study Gap')]//ancestor::div[@data-tut='false']"))
        )
        
        # Find all patient cards in the Study Gap section
        patient_cards = study_gap_section.find_elements(
            By.XPATH, ".//div[contains(@class, 'jss736')]")
        
        for card in patient_cards:
            patient_name = card.text.strip()
            if patient_name:  # Only add non-empty names
                study_gap_patients.append(patient_name)
                
    except TimeoutException:
        print("No Study Gap section found for this category")
    except Exception as e:
        print(f"Error extracting Study Gap patients: {e}")
    
    return study_gap_patients

def click_category_filter(driver, category_name):
    """Click on a specific category filter"""
    try:
        # Look for the System dropdown filter
        system_filter = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//input[@placeholder='System']//parent::div"))
        )
        system_filter.click()
        
        # Wait for dropdown options to appear
        time.sleep(1)
        
        # Click on the specific category
        category_option = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, f"//div[contains(text(), '{category_name}')]"))
        )
        category_option.click()
        
        # Wait for page to update
        time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"Error clicking category {category_name}: {e}")
        return False

def clear_filters(driver):
    """Clear all applied filters"""
    try:
        # Click on System filter to open dropdown
        system_filter = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//input[@placeholder='System']//parent::div"))
        )
        system_filter.click()
        time.sleep(1)
        
        # Look for "Clear" or similar option, or click outside to close
        # For now, just click outside to close the dropdown
        driver.find_element(By.TAG_NAME, "body").click()
        time.sleep(1)
        
    except Exception as e:
        print(f"Error clearing filters: {e}")

def main():
    """Main function to scrape Study Gap patients by category"""
    
    # Define medical categories to check
    categories = [
        "Cardiology",
        "Dermatology", 
        "Endocrine",
        "Gastrointestinal",
        "Genitourinary",
        "Haematology",
        "Infectious Diseases",
        "Musculoskeletal",
        "Neurology",
        "Obstetrics & Gynaecology",
        "Ophthalmology",
        "Paediatrics",
        "Psychiatry",
        "Respiratory",
        "Surgery"
    ]
    
    driver = setup_driver()
    
    try:
        # Login to the platform
        login_to_oscer(driver)
        
        # Navigate to home page where categories are
        driver.get("https://www.oscer.ai/dashboard/home")
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@placeholder='System']"))
        )
        
        results = {}
        
        # Go through each category
        for category in categories:
            print(f"\n--- Checking category: {category} ---")
            
            # Clear any existing filters first
            clear_filters(driver)
            
            # Click on the category filter
            if click_category_filter(driver, category):
                # Extract Study Gap patients
                patients = get_study_gap_patients(driver)
                
                if patients:
                    print(f"Found {len(patients)} patients in Study Gap:")
                    for patient in patients:
                        print(f"  - {patient}")
                    results[category] = patients
                else:
                    print("No patients found in Study Gap for this category")
                    results[category] = []
            else:
                print(f"Could not access category: {category}")
                results[category] = []
            
            # Sleep between categories as requested
            print("Waiting 1 second before next category...")
            time.sleep(1)
        
        # Save results to JSON file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"study_gap_patients_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                "scraped_at": datetime.now().isoformat(),
                "categories": results
            }, f, indent=4, ensure_ascii=False)
        
        print(f"\nResults saved to {filename}")
        
        # Print summary
        print("\n=== SUMMARY ===")
        total_patients = 0
        for category, patients in results.items():
            count = len(patients)
            total_patients += count
            print(f"{category}: {count} patients")
        
        print(f"\nTotal patients across all categories: {total_patients}")
        
    finally:
        input("Press Enter to close the browser...")
        driver.quit()

if __name__ == "__main__":
    main()
